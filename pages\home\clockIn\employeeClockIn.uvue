
<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
	<view class="clock-in-container">
		
		<view class="employee-info">
			<view class="employee-header">
				<text class="employee-name">张某某</text>
				<text class="employee-title">土建工程师</text>
			</view>
			<view class="department-info">
				<text class="department-label">所属项目部：</text>
				<text class="department-name">一二三项目部</text>
			</view>
		</view>

		
		<view class="shift-section">
			<text class="shift-title">早班：</text>
			<view class="shift-times">
				<view class="shift-time-item">
					<view class="shift_item_time">
						<text class="shift-time">上班08：00</text>
						<image class="shift-time-img" src="/static/image/xz.png" mode=""></image>
					</view>
					
					<view class="status-indicator">
						<text class="status-text">(正常)</text>
					</view>
				</view>
				<view class="shift-time-item">
					<view class="shift_item_time">
						<text class="shift-time">下班08：00</text>
						<!-- <image class="shift-time-img" src="/static/image/xz.png" mode=""></image> -->
					</view>
					<text class="status-text unpunched">未打卡</text>
				</view>
			</view>
		</view>

		
		<view class="clock-in-section">
			<view class="clock-in-button" @click="handleClockIn">
				<text class="clock-in-text">下班打卡</text>
				<text class="countdown-text">{{ countdownTime }}</text>
			</view>
		</view>

		
		<view class="location-status">
			<view class="location-indicator" @click="retryLocation">
				<image v-if="!locationLoading && !locationError" class="clockImg" src="/static/image/xz.png" mode=""></image>
				<view v-if="locationLoading" class="loading-indicator"></view>
				<image v-if="locationError" class="error-icon" src="/static/image/error.png" mode=""></image>
				<text class="location-text" :class="{ 'error': locationError, 'loading': locationLoading }">{{ locationText }}</text>
			</view>
		</view>

		<view class="records-button-container">
			<view class="records-button" @click="viewRecords">
				<text class="records-button-text">查看打卡记录</text>
			</view>
		</view>
	</view>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, onMounted, onUnmounted } from 'vue'

	// Reactive data
	const countdownTime = ref<string>('10:30:25')
	const locationText = ref<string>('正在获取位置信息...')
	const locationLoading = ref<boolean>(true)
	const locationError = ref<boolean>(false)
	let timer: number | null = null
	
	
	const handleLocationError = (errorMessage: string): void => {
		locationLoading.value = false
		locationError.value = true
		locationText.value = errorMessage
	}
	

	// Location functions
	const getAddressFromCoordinates = (latitude: number, longitude: number): void => {
		// For demo purposes, simulate address resolution
		// In production, you would integrate with a geocoding service
		setTimeout((): void => {
			const latStr = latitude.toString().substring(0, 8)
			const lngStr = longitude.toString().substring(0, 9)
			const mockAddress = `北京市朝阳区建国路(${latStr},${lngStr})`

			locationText.value = `已进入打卡位置：${mockAddress}`
			locationLoading.value = false
			locationError.value = false
		}, 1500)
	}

	const getCurrentLocation = (): void => {
		locationLoading.value = true
		locationError.value = false
		locationText.value = '正在获取位置信息...'

		uni.getLocation({
			type: 'gcj02',
			altitude: true,
			success: (res: any): void => {
				// Use safe property access for any type
				const resObj = res as UTSJSONObject
				const latitude = resObj.getNumber('latitude')
				const longitude = resObj.getNumber('longitude')

				if (latitude != null && longitude != null) {
					// Get address from coordinates
					getAddressFromCoordinates(latitude, longitude)
				} else {
					handleLocationError('位置坐标获取失败')
				}
			},
			fail: (error: any): void => {
				console.log('Location error:', error)
				// Use safe property access for error object
				const errorObj = error as UTSJSONObject
				const errorMsg = errorObj.getString('errMsg')
				if (errorMsg != null && errorMsg.includes('auth')) {
					uni.showModal({
						title: '位置权限',
						content: '需要获取您的位置信息进行打卡，请在系统设置中开启位置权限后重试',
						confirmText: '重试',
						cancelText: '取消',
						success: (modalRes: any): void => {
							const modalObj = modalRes as UTSJSONObject
							const confirmResult = modalObj.getBoolean('confirm')
							if (confirmResult != null && confirmResult) {
								// Retry after user potentially enables permission
								setTimeout((): void => {
									getCurrentLocation()
								}, 1000)
							} else {
								handleLocationError('位置权限被拒绝')
							}
						}
					})
				} else if (errorMsg != null && errorMsg.includes('timeout')) {
					handleLocationError('位置获取超时，请重试')
				} else {
					handleLocationError('位置获取失败，请检查GPS设置')
				}
			}
		})
	}



	
	const retryLocation = (): void => {
		if (!locationLoading.value) {
			getCurrentLocation()
		}
	}

	// Initialize countdown
	const initCountdown = (): void => {
		// Set initial countdown time
		let hours = 10
		let minutes = 30
		let seconds = 25

		timer = setInterval((): void => {
			seconds--
			if (seconds < 0) {
				seconds = 59
				minutes--
				if (minutes < 0) {
					minutes = 59
					hours--
					if (hours < 0) {
						// Countdown finished
						const currentTimer = timer
						if (currentTimer != null) {
							clearInterval(currentTimer)
							timer = null
						}
						return
					}
				}
			}

			const hoursStr = hours.toString().padStart(2, '0')
			const minutesStr = minutes.toString().padStart(2, '0')
			const secondsStr = seconds.toString().padStart(2, '0')
			countdownTime.value = `${hoursStr}:${minutesStr}:${secondsStr}`
		}, 1000)
	}

	// Event handlers
	const handleClockIn = (event: any): void => {
		// Check if location is available and valid
		if (locationLoading.value) {
			uni.showToast({
				title: '正在获取位置信息，请稍候',
				icon: 'none'
			})
			return
		}

		if (locationError.value) {
			uni.showModal({
				title: '位置信息异常',
				content: '无法获取当前位置，是否重新获取位置信息？',
				success: (res: any): void => {
					if (res.confirm) {
						getCurrentLocation()
					}
				}
			})
			return
		}

		// Proceed with clock-in
		uni.showToast({
			title: '打卡成功',
			icon: 'success'
		})
	}

	const viewRecords = (event: any): void => {
		uni.navigateTo({
			url: '/pages/home/<USER>/records'
		})
	}

	// Lifecycle hooks
	onMounted((): void => {
		initCountdown()
		getCurrentLocation()
	})

	onUnmounted((): void => {
		const currentTimer = timer
		if (currentTimer != null) {
			clearInterval(currentTimer)
			timer = null
		}
	})
</script>

<style lang="scss" scoped>
.clock-in-container {
	display: flex;
	flex-direction: column;
	padding: 40rpx;
	background-color: #f5f5f5;
	
}

.employee-info {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
}

.employee-header {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 24rpx;
}

.employee-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
	margin-right: 24rpx;
}

.employee-title {
	font-size: 28rpx;
	color: #ff9500;
	background-color: #fff3e0;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
}

.department-info {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.department-label {
	font-size: 28rpx;
	color: #999999;
}

.department-name {
	font-size: 28rpx;
	color: #333333;
}

.shift-section {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 64rpx;
}

.shift-title {
	font-size: 32rpx;
	color: #333333;
	// font-weight: bold;
	margin-bottom: 24rpx;
}

.shift-times {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	width: 90%;
	margin: 0 auto;
}

.shift-time-item {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}
.shift_item_time{
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 12rpx;
	.shift-time-img{
		width: 25rpx;
		height: 25rpx;
		margin-left: 10rpx;
	}
}
.shift-time {
	font-size: 28rpx;
	color: #333333;
	
}

.status-indicator {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

// .status-indicator.normal {
// 	background-color: #e8f5e8;
// }

.status-dot {
	width: 12rpx;
	height: 12rpx;
	background-color: #00C268;
	border-radius: 12rpx;
	margin-right: 8rpx;
}

.status-text {
	font-size: 24rpx;
	color: #00C268;
}

.status-text.unpunched {
	color: #999999;
}

.clock-in-section {
	display: flex;
	flex-direction: row;
	justify-content: center;
	margin-bottom: 64rpx;
}

.clock-in-button {
	width: 320rpx;
	height: 320rpx;
	background-color: #2196f3;
	border-radius: 320rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	box-shadow: 0 8rpx 24rpx rgba(33, 150, 243, 0.3);
}

.clock-in-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: bold;
	margin-bottom: 16rpx;
}

.countdown-text {
	font-size: 28rpx;
	color: #ffffff;
}

.location-status {
	display: flex;
	justify-content: center;
	margin-bottom: 64rpx;
}

.location-indicator {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	.clockImg{
		width: 25rpx;
		height: 25rpx;
		margin-right: 10rpx;
	}
	.error-icon{
		width: 25rpx;
		height: 25rpx;
		margin-right: 10rpx;
	}
}

.location-dot {
	width: 16rpx;
	height: 16rpx;
	background-color: #4caf50;
	border-radius: 16rpx;
	margin-right: 12rpx;
}

.loading-indicator {
	width: 25rpx;
	height: 25rpx;
	border: 3rpx solid #f3f3f3;
	border-top: 3rpx solid #2196f3;
	border-radius: 50%;
	margin-right: 10rpx;
}

.location-text {
	font-size: 28rpx;
	color: #00C268;
}

.location-text.loading {
	color: #2196f3;
}

.location-text.error {
	color: #ff5722;
}

.records-button-container {
	display: flex;
	flex-direction: row;
	justify-content: center;
	margin-top: 40rpx;
}

.records-button {
	background-color: #2196f3;
	border-radius: 48rpx;
	padding: 24rpx 80rpx;
	width: 100%;
	max-width: 600rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.records-button-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: bold;
}
</style>
