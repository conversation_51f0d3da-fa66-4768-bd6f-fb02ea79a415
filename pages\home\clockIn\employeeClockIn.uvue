<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
	<view class="clock-in-container">
		<!-- Employee Info Section -->
		<view class="employee-info">
			<view class="employee-header">
				<text class="employee-name">张某某</text>
				<text class="employee-title">土建工程师</text>
			</view>
			<view class="department-info">
				<text class="department-label">所属项目部：</text>
				<text class="department-name">一二三项目部</text>
			</view>
		</view>

		<!-- Work Shift Section -->
		<view class="shift-section">
			<text class="shift-title">早班：</text>
			<view class="shift-times">
				<view class="shift-time-item">
					<text class="shift-time">上班08：00</text>
					<view class="status-indicator normal">
						<view class="status-dot"></view>
						<text class="status-text">正常</text>
					</view>
				</view>
				<view class="shift-time-item">
					<text class="shift-time">下班08：00</text>
					<text class="status-text unpunched">未打卡</text>
				</view>
			</view>
		</view>

		<!-- Clock In Button Section -->
		<view class="clock-in-section">
			<view class="clock-in-button" @click="handleClockIn">
				<text class="clock-in-text">下班打卡</text>
				<text class="countdown-text">{{ countdownTime }}</text>
			</view>
		</view>

		<!-- Location Status -->
		<view class="location-status">
			<view class="location-indicator">
				<view class="location-dot"></view>
				<text class="location-text">已进入打卡位置：某某某某地</text>
			</view>
		</view>

		<!-- View Records Button -->
		<view class="records-button-container">
			<view class="records-button" @click="viewRecords">
				<text class="records-button-text">查看打卡记录</text>
			</view>
		</view>
	</view>

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, onMounted, onUnmounted } from 'vue'

	// Reactive data
	const countdownTime = ref<string>('10:30:25')
	let timer: number | null = null

	// Initialize countdown
	const initCountdown = (): void => {
		// Set initial countdown time
		let hours = 10
		let minutes = 30
		let seconds = 25

		timer = setInterval((): void => {
			seconds--
			if (seconds < 0) {
				seconds = 59
				minutes--
				if (minutes < 0) {
					minutes = 59
					hours--
					if (hours < 0) {
						// Countdown finished
						if (timer != null) {
							clearInterval(timer)
							timer = null
						}
						return
					}
				}
			}

			const hoursStr = hours.toString().padStart(2, '0')
			const minutesStr = minutes.toString().padStart(2, '0')
			const secondsStr = seconds.toString().padStart(2, '0')
			countdownTime.value = `${hoursStr}:${minutesStr}:${secondsStr}`
		}, 1000)
	}

	// Event handlers
	const handleClockIn = (event: any): void => {
		uni.showToast({
			title: '打卡成功',
			icon: 'success'
		})
	}

	const viewRecords = (event: any): void => {
		uni.navigateTo({
			url: '/pages/home/<USER>/records'
		})
	}

	// Lifecycle hooks
	onMounted((): void => {
		initCountdown()
	})

	onUnmounted((): void => {
		if (timer != null) {
			clearInterval(timer)
			timer = null
		}
	})
</script>

<style lang="scss" scoped>
.clock-in-container {
	display: flex;
	flex-direction: column;
	padding: 40rpx;
	background-color: #f5f5f5;
	min-height: 100%;
}

.employee-info {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
}

.employee-header {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 24rpx;
}

.employee-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
	margin-right: 24rpx;
}

.employee-title {
	font-size: 28rpx;
	color: #ff9500;
	background-color: #fff3e0;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
}

.department-info {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.department-label {
	font-size: 28rpx;
	color: #999999;
}

.department-name {
	font-size: 28rpx;
	color: #333333;
}

.shift-section {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 64rpx;
}

.shift-title {
	font-size: 32rpx;
	color: #333333;
	font-weight: bold;
	margin-bottom: 24rpx;
}

.shift-times {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}

.shift-time-item {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.shift-time {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 12rpx;
}

.status-indicator {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.status-indicator.normal {
	background-color: #e8f5e8;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	background-color: #4caf50;
	border-radius: 50%;
	margin-right: 8rpx;
}

.status-text {
	font-size: 24rpx;
	color: #4caf50;
}

.status-text.unpunched {
	color: #999999;
}

.clock-in-section {
	display: flex;
	justify-content: center;
	margin-bottom: 64rpx;
}

.clock-in-button {
	width: 320rpx;
	height: 320rpx;
	background-color: #2196f3;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	box-shadow: 0 8rpx 24rpx rgba(33, 150, 243, 0.3);
}

.clock-in-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: bold;
	margin-bottom: 16rpx;
}

.countdown-text {
	font-size: 28rpx;
	color: #ffffff;
}

.location-status {
	display: flex;
	justify-content: center;
	margin-bottom: 64rpx;
}

.location-indicator {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.location-dot {
	width: 16rpx;
	height: 16rpx;
	background-color: #4caf50;
	border-radius: 50%;
	margin-right: 12rpx;
}

.location-text {
	font-size: 28rpx;
	color: #4caf50;
}

.records-button-container {
	display: flex;
	justify-content: center;
}

.records-button {
	background-color: #2196f3;
	border-radius: 48rpx;
	padding: 24rpx 80rpx;
	width: 100%;
	max-width: 600rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.records-button-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: bold;
}
</style>
