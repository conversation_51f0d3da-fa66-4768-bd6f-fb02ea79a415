<template>
	<view class="status_bar">
		<!-- 这里是状态栏 -->
	</view>
	<!-- #ifdef APP -->
	<scroll-view
		style="flex: 1; background-color: #f5f5f5;"
		scroll-y="true"
		show-scrollbar="false"
	>	
	<!-- #endif -->
	<!-- #ifndef APP -->
	<view class="web-scroll-container">
	<!-- #endif -->
		<view class="home-container">
			<!-- 顶部蓝色区域 -->
			<view class="header-section">
				<!-- 背景图片层 -->
				<image
					class="header-background-image"
					src="/static/image/bj.png"
					mode="aspectFill"
					@error="handleImageError"
					@load="handleImageLoad"
				></image>

				<!-- 渐变遮罩层（备选方案） -->
				<view class="header-gradient-overlay"></view>

				<!-- 内容层 -->
				<view class="header-content">
					<!-- 项目部名称选择器 -->
					<view class="department-selector" @click="handleDepartmentClick">
						<text class="department-text">{{ currentDepartment }}</text>
						<text class="dropdown-icon">▼</text>
					</view>

					<!-- 主标题 -->
					<view class="main-title-container">
						<text class="main-title">翔翔任务平台</text>
					</view>

					<!-- 装饰图案占位 -->
					<view class="decoration-placeholder"></view>
												<!-- 通知栏 -->
			<view class="notice-bar">
				<!-- 通知图标占位 -->
				<view class="notice-icon-placeholder"></view>
				<scroll-view
					class="notice-scroll"
					scroll-x="true"
					:scroll-left="scrollLeft"
					show-scrollbar="false"
				>
					<view class="notice-content">
						<text class="notice-text">{{ currentNotice }}</text>
					</view>
				</scroll-view>
			</view>
				</view>

			</view>



			<!-- 功能卡片列表 -->
			<view class="card-list">
				<view class="card-item" @click="handleCardClick('employee')">
					<view class="card-content">
						<view class="card-text-section">
							<text class="card-title">员工打卡</text>
							<text class="card-desc">按时打卡，保证出勤，提高效率</text>
						</view>
						<view class="card-icon-placeholder card-icon-clock"></view>
					</view>
				</view>

				<view class="card-item" @click="handleCardClick('daily')">
					<view class="card-content">
						<view class="card-text-section">
							<text class="card-title">我的日报</text>
							<text class="card-desc">按时打卡，保证出勤，提高效率</text>
						</view>
						<view class="card-icon-placeholder card-icon-report"></view>
					</view>
				</view>

				<view class="card-item" @click="handleCardClick('weekly')">
					<view class="card-content">
						<view class="card-text-section">
							<text class="card-title">我的周考核</text>
							<text class="card-desc">按时打卡，保证出勤，提高效率</text>
						</view>
						<view class="card-icon-placeholder card-icon-weekly"></view>
					</view>
				</view>

				<view class="card-item" @click="handleCardClick('monthly')">
					<view class="card-content">
						<view class="card-text-section">
							<text class="card-title">我的月考核</text>
							<text class="card-desc">按时打卡，保证出勤，提高效率</text>
						</view>
						<view class="card-icon-placeholder card-icon-monthly"></view>
					</view>
				</view>
				<view class="card-item" @click="handleCardClick('task')">
					<view class="card-content">
						<view class="card-text-section">
							<text class="card-title">任务下发</text>
							<text class="card-desc"></text>
						</view>
						<view class="card-icon-placeholder card-icon-monthly"></view>
					</view>
				</view>
			</view>

			

			<!-- 底部安全区域 -->
			<view class="bottom-safe-area"></view>
		</view>

		<!-- 项目部选择弹框 -->
		<ProjectSelector
			:show="showProjectSelector"
			@close="handleProjectSelectorClose"
			@confirm="handleProjectSelectorConfirm"
		/>

		

	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
	<!-- #ifndef APP -->
	</view>
	<!-- #endif -->
</template>

<script setup>

	import ProjectSelector from '@/components/ProjectSelector.uvue'
	
	import type { ProjectOption } from '@/utils/apiType.uts'

	// 项目部选择相关
	const showProjectSelector = ref<boolean>(false)
	const currentDepartment = ref<string>('一三项目部')



	// 通知消息数据
	const noticeList = ref<string[]>([
		'通知：您接收到了新的任务，请查看......',
		'提醒：本月考核即将截止，请尽快完成相关考核任务',
		'公告：系统将于今晚22:00-24:00进行维护升级，请提前做好相关准备',
		'通知：新版本功能已上线，欢迎体验新的打卡和报告功能',
		'提醒：请及时更新个人信息，确保系统数据的准确性'
	])

	// 当前显示的通知索引
	const currentNoticeIndex = ref<number>(0)

	// 当前显示的通知内容
	const currentNotice = computed<string>(() => {
		const index = currentNoticeIndex.value
		if (index >= 0 && index < noticeList.value.length) {
			return noticeList.value[index]
		}
		return ''
	})

	// 滚动位置
	const scrollLeft = ref<number>(0)

	// 通知切换定时器
	let noticeTimer: number | null = null

	// 滚动动画定时器
	let scrollTimer: number | null = null

	// 启动滚动动画
	const startScrollAnimation = (): void => {
		if (scrollTimer !== null) {
			clearInterval(scrollTimer as number)
		}

		scrollTimer = setInterval(() => {
			scrollLeft.value += 2
			// 当滚动到一定位置时重置，实现循环滚动
			if (scrollLeft.value > 800) {
				scrollLeft.value = 0
			}
		}, 50)
	}

	// 停止滚动动画
	const stopScrollAnimation = (): void => {
		if (scrollTimer !== null) {
			clearInterval(scrollTimer as number)
			scrollTimer = null
		}
	}

	// 启动通知轮播
	const startNoticeRotation = (): void => {
		if (noticeTimer !== null) {
			clearInterval(noticeTimer as number)
		}

		// 启动滚动动画
		startScrollAnimation()

		// 每8秒切换一条通知
		noticeTimer = setInterval(() => {
			currentNoticeIndex.value = (currentNoticeIndex.value + 1) % noticeList.value.length
			// 切换通知时重置滚动位置
			scrollLeft.value = 0
		}, 8000)
	}

	// 停止通知轮播
	const stopNoticeRotation = (): void => {
		if (noticeTimer !== null) {
			clearInterval(noticeTimer as number)
			noticeTimer = null
		}
		stopScrollAnimation()
	}

	// 背景图片加载成功处理
	const handleImageLoad = (): void => {
		console.log('背景图片加载成功')
	}

	// 背景图片加载失败处理
	const handleImageError = (): void => {
		console.log('背景图片加载失败，使用渐变背景作为备选方案')
		// 可以在这里添加备选方案的逻辑
	}

	// 项目部选择器点击事件
	const handleDepartmentClick = (): void => {
		console.log('点击项目部选择器')
		showProjectSelector.value = true
	}

	// 项目部选择器关闭事件
	const handleProjectSelectorClose = (): void => {
		console.log('关闭项目部选择器')
		showProjectSelector.value = false
	}

	// 项目部选择器确认事件
	const handleProjectSelectorConfirm = (selectedProject: ProjectOption | null): void => {
		console.log('确认选择项目部:', selectedProject)
		if (selectedProject !== null) {
			currentDepartment.value = selectedProject.label
		}
		showProjectSelector.value = false
	}

	// 处理卡片点击事件
	const handleCardClick = (type: string): void => {
		console.log('点击了卡片:', type)

		// 根据不同类型跳转到对应页面
		switch (type) {
			case 'employee':
				uni.navigateTo({
					url: '/pages/home/<USER>/employeeClockIn'
				})
				break
			case 'daily':
				uni.navigateTo({
					url: '/pages/report/daily'
				})
				break
			case 'weekly':
				uni.navigateTo({
					url: '/pages/home/<USER>/examIndex?type=week'
				})
				break
			case 'monthly':
				uni.navigateTo({
					url: '/pages/home/<USER>/examIndex?type=month'
				})
				break
			case 'task':
				uni.navigateTo({
					url: '/pages/task/assign'
				})
				break
			default:
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				})
		}
	}


	// 页面加载生命周期
	onLoad(() => {
		console.log('首页加载完成')
	})

	// #ifdef APP
	// 页面显示生命周期
	onShow(() => {
		console.log('首页显示')
		// 页面显示时启动通知轮播
		startNoticeRotation()
	})
	// #endif

	// 页面隐藏生命周期
	onHide(() => {
		console.log('首页隐藏')
		// 页面隐藏时停止通知轮播
		stopNoticeRotation()
	})

	// 页面卸载生命周期
	onUnload(() => {
		console.log('首页卸载')
		// 页面卸载时清理定时器
		stopNoticeRotation()
	})

</script>

<style>
	.home-container {
		width: 100%;
		background-color: #f5f5f5;
		flex-direction: column;
	}

	/* 顶部蓝色区域 */
	.header-section {
		position: relative;
		height: 400rpx;
		overflow: hidden;
	}

	/* 背景图片样式 */
	.header-background-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	/* 渐变遮罩层（备选方案/增强效果） */
	.header-gradient-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, rgba(30, 136, 229, 0.6) 0%, rgba(66, 165, 245, 0.4) 100%);
		z-index: 2;
	}

	/* 当图片加载失败时的备选背景 */
	.header-section.image-error .header-gradient-overlay {
		background: linear-gradient(135deg, #1E88E5 0%, #42A5F5 100%);
	}

	/* 内容层 */
	.header-content {
		position: relative;
		z-index: 3;
		padding: 40rpx 30rpx 60rpx 30rpx;
		height: 100%;
		flex-direction: column;
		justify-content: space-between;
	}

	/* 项目部选择器样式 */
	.department-selector {
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		margin-bottom: 40rpx;
		padding: 10rpx 20rpx;
		border-radius: 20rpx;
		background-color: rgba(255, 255, 255, 0.1);
	}

	.department-selector:active {
		background-color: rgba(255, 255, 255, 0.2);
	}

	.department-text {
		font-size: 28rpx;
		color: #FFFFFF;
		margin-right: 10rpx;
	}

	.dropdown-icon {
		font-size: 24rpx;
		color: #FFFFFF;
	}

	/* 主标题容器 */
	.main-title-container {
		margin-bottom: 40rpx;
	}

	.main-title {
		font-size: 48rpx;
		font-weight: bold;
		color: #FFFFFF;
		line-height: 1.2;
	}

	/* 装饰图案占位 */
	.decoration-placeholder {
		position: absolute;
		right: 0;
		top: -20rpx;
		width: 120rpx;
		height: 120rpx;
		background-color: rgba(255, 255, 255, 0.15);
		border-radius: 60rpx;
	}

	/* 通知栏样式 */
	.notice-bar {
		background-color: #FFFFFF;
		border-radius: 25rpx;
		padding: 20rpx 30rpx;

		height: 80rpx;
		overflow: hidden;
		flex-direction: row;
		align-items: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.notice-icon-placeholder {
		width: 40rpx;
		height: 40rpx;
		background-color: #FFA726;
		border-radius: 20rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.notice-scroll {
		flex: 1;
		height: 100%;
	}

	.notice-content {
		flex-direction: row;
		align-items: center;
		height: 100%;
		min-width: 1000rpx;
	}

	.notice-text {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.4;
		white-space: nowrap;
		padding-right: 100rpx;
	}

	/* 卡片列表样式 */
	.card-list {
		width: 100%;
		flex-direction: column;
		padding: 0 30rpx;
	}

	.card-item {
		background-color: #f3f6fe;
		border-radius: 16rpx;
		padding: 40rpx 60rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	}

	.card-item:active {
		background-color: #F5F5F5;
	}

	.card-content {
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	.card-text-section {
		flex: 1;
		flex-direction: column;
		align-items: flex-start;
	}

	.card-title {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 8rpx;
	}

	.card-desc {
		font-size: 24rpx;
		color: #999999;
		line-height: 1.4;
	}

	/* 卡片图标占位样式 */
	.card-icon-placeholder {
		width: 60rpx;
		height: 60rpx;
		border-radius: 30rpx;
		flex-shrink: 0;
	}

	.card-icon-clock {
		background-color: #4CAF50;
	}

	.card-icon-report {
		background-color: #2196F3;
	}

	.card-icon-weekly {
		background-color: #FF9800;
	}

	.card-icon-monthly {
		background-color: #9C27B0;
	}

	/* 转办测试按钮容器 */
	.test-button-container {
		padding: 30rpx;
		align-items: center;
	}

	.test-button {
		width: 300rpx;
		height: 80rpx;
		background-color: #0189F6;
		color: #fff;
		border-radius: 8rpx;
		font-size: 30rpx;
		border: none;
	}

	/* 底部安全区域 */
	.bottom-safe-area {
		height: 100rpx;
		width: 100%;
	}

	/* Web端滚动容器 */
	.web-scroll-container {
		height: 100%;
		background-color: #f5f5f5;
	}
</style>
